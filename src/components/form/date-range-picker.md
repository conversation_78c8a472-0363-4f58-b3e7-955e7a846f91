# DateRangePicker Component

A reusable Vue 3 date range picker component that combines an Arco Design range picker with quick time selection buttons.

## Features

- **Date Range Selection**: Uses Arco Design's `a-range-picker` for date selection
- **Quick Time Buttons**: Provides buttons for common time ranges (昨天/今天/本周/本月)
- **6-Month Validation**: Enforces maximum 6-month date range selection
- **Vue 3 Composition API**: Built with modern Vue 3 patterns
- **TypeScript Support**: Fully typed with TypeScript
- **Event Emission**: Emits events for parent component integration

## Usage

### Basic Usage

```vue
<template>
  <a-form-item field="date_range" label="时间">
    <DateRangePicker
      v-model:model-value="searchForm.date_range"
      v-model:time-range="searchForm.time_range"
      @change="handleDateRangeChange" />
  </a-form-item>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import DateRangePicker from '@/components/form/date-range-picker.vue';

const searchForm = reactive({
  date_range: [],
  time_range: '',
});

const handleDateRangeChange = (dateRange: string[], timeRange: string) => {
  console.log('Date range changed:', dateRange, timeRange);
  // Add your custom logic here
};
</script>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | `string[]` | `[]` | The selected date range (v-model) |
| `timeRange` | `string` | `''` | The selected time range button |
| `placeholder` | `string[]` | `['开始时间', '结束时间']` | Placeholder text for date inputs |
| `pickerStyle` | `string \| Record<string, any>` | `'width: 300px; margin-right: 16px'` | Custom styles for the date picker |

### Events

| Event | Parameters | Description |
|-------|------------|-------------|
| `update:modelValue` | `value: string[]` | Emitted when date range changes |
| `update:timeRange` | `value: string` | Emitted when time range button is selected |
| `change` | `value: string[], timeRange: string` | Emitted when either date range or time range changes |

### Time Range Options

The component provides four quick selection buttons:

- **昨天** (`yesterday`): Selects yesterday's date
- **今天** (`today`): Selects today's date  
- **本周** (`week`): Selects current week (Monday to Sunday)
- **本月** (`month`): Selects current month

## Validation

The component automatically enforces a **6-month maximum** date range:

- When manually selecting dates, if the range exceeds 6 months, a warning message is shown
- The date selection is cleared and the user must select a valid range
- The `disabledDate` function prevents selection of dates outside the 6-month window

## Implementation Details

### Date Format
- All dates are formatted as `YYYY-MM-DD` using dayjs
- The component uses dayjs for all date calculations and formatting

### State Management
- When a time range button is clicked, it automatically sets the corresponding date range
- When dates are manually selected, the time range selection is cleared
- Invalid date ranges (>6 months) clear both date and time selections

### Styling
- Uses flexbox layout with `display: flex` and `align-items: center`
- Maintains consistent spacing between the date picker and button group
- Inherits Arco Design's styling for consistent UI

## Migration from Original Implementation

This component replaces the duplicated date picker logic in:
- `src/views/member/followup-detail.vue`
- `src/views/member/components/followup-coach-list.vue`

### Before (Duplicated Code)
```vue
<a-range-picker
  v-model="searchForm.date_range"
  style="width: 300px; margin-right: 16px"
  :placeholder="['开始时间', '结束时间']"
  :disabled-date="disabledDate"
  @change="handleDateRangeChange" />
<a-button-group>
  <a-button :type="searchForm.time_range === 'yesterday' ? 'primary' : 'outline'" @click="handleTimeRangeChange('yesterday')">昨天</a-button>
  <!-- ... more buttons ... -->
</a-button-group>
```

### After (Reusable Component)
```vue
<DateRangePicker
  v-model:model-value="searchForm.date_range"
  v-model:time-range="searchForm.time_range"
  @change="handleDateRangeChange" />
```

## Benefits

1. **Code Reusability**: Single component used across multiple views
2. **Maintainability**: Changes only need to be made in one place
3. **Consistency**: Ensures identical behavior across all usage locations
4. **Type Safety**: Full TypeScript support with proper prop and event typing
5. **Performance**: Reduced bundle size by eliminating duplicate code
