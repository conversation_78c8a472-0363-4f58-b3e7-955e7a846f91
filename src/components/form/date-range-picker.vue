<template>
  <div class="date-range-picker">
    <a-range-picker
      :model-value="modelValue"
      :style="pickerStyle"
      :placeholder="placeholder"
      :disabled-date="disabledDate"
      @change="handleDateRangeChange" />
    <a-button-group>
      <a-button :type="timeRange === 'yesterday' ? 'primary' : 'outline'" @click="handleTimeRangeChange('yesterday')">
        昨天
      </a-button>
      <a-button :type="timeRange === 'today' ? 'primary' : 'outline'" @click="handleTimeRangeChange('today')">
        今天
      </a-button>
      <a-button :type="timeRange === 'week' ? 'primary' : 'outline'" @click="handleTimeRangeChange('week')">
        本周
      </a-button>
      <a-button :type="timeRange === 'month' ? 'primary' : 'outline'" @click="handleTimeRangeChange('month')">
        本月
      </a-button>
    </a-button-group>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';

  // 定义 props
  interface Props {
    modelValue: string[];
    timeRange: string;
    placeholder?: string[];
    pickerStyle?: string | Record<string, any>;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: () => ['开始时间', '结束时间'],
    pickerStyle: 'width: 300px; margin-right: 16px',
  });

  // 定义 emits
  const emit = defineEmits<{
    'update:modelValue': [value: string[]];
    'update:timeRange': [value: string];
    'change': [value: string[], timeRange: string];
  }>();

  // 日期范围验证 - 最大6个月
  const validateDateRange = (startDate: string, endDate: string): boolean => {
    if (!startDate || !endDate) return true;

    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const diffInMonths = end.diff(start, 'month', true);

    return diffInMonths <= 6;
  };

  // 禁用日期函数
  const disabledDate = (current: any) => {
    if (!props.modelValue || props.modelValue.length === 0) {
      return false;
    }

    const selectedDate = props.modelValue[0];
    if (!selectedDate) return false;

    const currentDate = dayjs(current);
    const selectedStart = dayjs(selectedDate);

    // 如果已选择开始日期，限制结束日期不能超过6个月
    const maxEndDate = selectedStart.add(6, 'month');
    const minEndDate = selectedStart.subtract(6, 'month');

    return currentDate.isAfter(maxEndDate) || currentDate.isBefore(minEndDate);
  };

  // 时间范围变化处理
  const handleTimeRangeChange = (value: string) => {
    const today = dayjs();
    let newDateRange: string[] = [];

    switch (value) {
      case 'yesterday':
        newDateRange = [today.subtract(1, 'day').format('YYYY-MM-DD'), today.subtract(1, 'day').format('YYYY-MM-DD')];
        break;
      case 'today':
        newDateRange = [today.format('YYYY-MM-DD'), today.format('YYYY-MM-DD')];
        break;
      case 'week':
        newDateRange = [today.startOf('week').format('YYYY-MM-DD'), today.endOf('week').format('YYYY-MM-DD')];
        break;
      case 'month':
        newDateRange = [today.startOf('month').format('YYYY-MM-DD'), today.endOf('month').format('YYYY-MM-DD')];
        break;
      default:
        break;
    }

    emit('update:timeRange', value);
    emit('update:modelValue', newDateRange);
    emit('change', newDateRange, value);
  };

  // 日期范围变化处理
  const handleDateRangeChange = (_value: any, _date: any, dateStrings?: (string | undefined)[]) => {
    if (!dateStrings || dateStrings.length !== 2) {
      return;
    }

    const [startDate, endDate] = dateStrings;

    if (startDate && endDate && !validateDateRange(startDate, endDate)) {
      Message.warning('日期范围不能超过6个月，请重新选择');
      // 清空日期选择
      emit('update:modelValue', []);
      // 清空时间范围选择
      emit('update:timeRange', '');
      emit('change', [], '');
      return;
    }

    // 如果验证通过，清空时间范围选择（因为用户手动选择了日期）
    const newDateRange = dateStrings as string[];
    emit('update:modelValue', newDateRange);
    emit('update:timeRange', '');
    emit('change', newDateRange, '');
  };
</script>

<style scoped lang="less">
  .date-range-picker {
    display: flex;
    align-items: center;
    gap: 0;
  }
</style>
