<template>
  <!-- Search Form -->
  <a-card class="general-card">
    <a-form
      :model="searchForm"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
      label-align="left"
      auto-label-width>
      <a-row :gutter="16">
        <a-col :flex="1">
          <a-form-item field="date_range" label="时间">
            <a-range-picker
              v-model="searchForm.date_range"
              style="width: 300px; margin-right: 16px"
              :placeholder="['开始时间', '结束时间']"
              :disabled-date="disabledDate"
              @change="handleDateRangeChange" />
            <a-button-group>
              <a-button
                :type="searchForm.time_range === 'yesterday' ? 'primary' : 'outline'"
                @click="handleTimeRangeChange('yesterday')">
                昨天
              </a-button>
              <a-button
                :type="searchForm.time_range === 'today' ? 'primary' : 'outline'"
                @click="handleTimeRangeChange('today')">
                今天
              </a-button>
              <a-button
                :type="searchForm.time_range === 'week' ? 'primary' : 'outline'"
                @click="handleTimeRangeChange('week')">
                本周
              </a-button>
              <a-button
                :type="searchForm.time_range === 'month' ? 'primary' : 'outline'"
                @click="handleTimeRangeChange('month')">
                本月
              </a-button>
            </a-button-group>
          </a-form-item>
        </a-col>
        <a-col :flex="1">
          <a-form-item field="coach_id" label="教练">
            <SalesSelect
              v-model="searchForm.coach_id"
              :belong-bus-id="searchForm.bus_id"
              label-in-value
              style="width: 300px"
              :is-membership="false"
              is-pt-coach
              is-swim-coach
              show-coach-type
              placeholder="请选择"></SalesSelect>
          </a-form-item>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="24" style="display: flex; align-items: center; justify-content: flex-end">
          <a-button @click="handleExport">导出Excel</a-button>
        </a-col>
      </a-row>
    </a-form>

    <!-- Table Section -->
    <a-table
      :loading="loading"
      :data="tableData"
      :pagination="pagination"
      :bordered="false"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange">
      <template #columns>
        <a-table-column title="序号" data-index="index" :width="80" align="center">
          <template #cell="{ rowIndex }">
            {{ (pagination.page_no - 1) * pagination.page_size + rowIndex + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="教练名称" data-index="coach_name" align="center" />
        <a-table-column title="跟进人数" data-index="follow_user_num" align="center">
          <template #cell="{ record }">
            <span style="color: #1890ff; cursor: pointer" @click="handleViewMembers(record)">
              {{ record.follow_user_num }}
            </span>
          </template>
        </a-table-column>
        <a-table-column title="跟进次数" data-index="follow_num" align="center">
          <template #cell="{ record }">
            <span style="color: #1890ff; cursor: pointer" @click="handleViewTimes(record)">
              {{ record.follow_num }}
            </span>
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="100" align="center">
          <template #cell="{ record }">
            <a-link @click="handleViewDetail(record)">详情</a-link>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import { useBusInfoStore } from '@/store';
  import SalesSelect from '@/components/membership/salesSelect.vue';
  import {
    getFollowupCoachList,
    exportFollowupCoachData,
    type FollowupCoachData,
    type FollowupCoachSearchParams,
  } from '@/api/followup';

  // 定义接口类型
  interface SearchForm {
    date_range: string[];
    time_range: string;
    coach_id: string;
    bus_id: string;
  }

  // 响应式数据
  const loading = ref(false);
  const tableData = ref<FollowupCoachData[]>([]);

  const busInfo = useBusInfoStore();
  const router = useRouter();

  // 搜索表单
  const searchForm = reactive<SearchForm>({
    date_range: [],
    time_range: '',
    coach_id: '',
    bus_id: busInfo.bus_id,
  });

  // 分页配置
  const pagination = reactive({
    page_no: 1,
    page_size: 10,
    total: 0,
    showPageSize: true,
    showTotal: true,
  });

  // API 实例
  const { execute: executeGetList } = getFollowupCoachList();
  const { execute: executeExport } = exportFollowupCoachData();

  // 使用模拟数据作为后备
  const mockIt = () => {
    const mockData = {
      list: [
        {
          coach_id: 708,
          coach_name: '张教练',
          follow_user_num: 3,
          follow_num: 5,
        },
      ],
      total: 1,
    };

    tableData.value = mockData.list;
    pagination.total = mockData.total;
  };

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const params: FollowupCoachSearchParams = {
        start_time: searchForm.date_range[0] || '',
        end_time: searchForm.date_range[1] || '',
        coach_id: searchForm.coach_id,
        page_no: pagination.page_no,
        page_size: pagination.page_size,
      };

      const { data } = await executeGetList({ data: params });

      if (data.value) {
        tableData.value = data.value.list;
        pagination.total = data.value.total;
      }

      mockIt();
    } catch (error) {
      console.error('获取数据失败:', error);
      mockIt();
    } finally {
      loading.value = false;
    }
  };

  // 方法定义
  const handleTimeRangeChange = (value: string) => {
    const today = dayjs();
    searchForm.time_range = value;

    switch (value) {
      case 'yesterday':
        searchForm.date_range = [
          today.subtract(1, 'day').format('YYYY-MM-DD'),
          today.subtract(1, 'day').format('YYYY-MM-DD'),
        ];
        break;
      case 'today':
        searchForm.date_range = [today.format('YYYY-MM-DD'), today.format('YYYY-MM-DD')];
        break;
      case 'week':
        searchForm.date_range = [today.startOf('week').format('YYYY-MM-DD'), today.endOf('week').format('YYYY-MM-DD')];
        break;
      case 'month':
        searchForm.date_range = [
          today.startOf('month').format('YYYY-MM-DD'),
          today.endOf('month').format('YYYY-MM-DD'),
        ];
        break;
      default:
        break;
    }
  };

  const handleSearch = async () => {
    pagination.page_no = 1;
    await fetchData();
  };

  const handleExport = async () => {
    try {
      const params: FollowupCoachSearchParams = {
        start_time: searchForm.date_range[0] || '',
        end_time: searchForm.date_range[1] || '',
        coach_id: searchForm.coach_id,
        _export: 1,
      };

      await executeExport({ data: params });
      // 导出成功的处理逻辑
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  const handlePageChange = (page: number) => {
    pagination.page_no = page;
    fetchData();
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.page_size = pageSize;
    pagination.page_no = 1;
    fetchData();
  };

  const handleViewMembers = (record: FollowupCoachData) => {
    // TODO: 跳转到跟进人数详情页面
    console.log('查看跟进人数详情', record);
    router.push({
      name: 'MemberFollowupDetail',
      params: {
        coachId: record.coach_id,
      },
    });
  };

  const handleViewTimes = (record: FollowupCoachData) => {
    // TODO: 跳转到跟进次数详情页面
    console.log('查看跟进次数详情', record);
    router.push({
      name: 'MemberFollowupDetail',
      params: {
        coachId: record.coach_id,
      },
    });
  };

  const handleViewDetail = (record: FollowupCoachData) => {
    // TODO: 跳转到详情页面
    console.log('查看详情', record);
    router.push({
      name: 'MemberFollowupDetail',
      params: {
        coachId: record.coach_id,
      },
    });
  };

  // 日期范围验证 - 最大6个月
  const validateDateRange = (startDate: string, endDate: string): boolean => {
    if (!startDate || !endDate) return true;

    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const diffInMonths = end.diff(start, 'month', true);

    return diffInMonths <= 6;
  };

  // 禁用日期函数
  const disabledDate = (current: any) => {
    if (!searchForm.date_range || searchForm.date_range.length === 0) {
      return false;
    }

    const selectedDate = searchForm.date_range[0];
    if (!selectedDate) return false;

    const currentDate = dayjs(current);
    const selectedStart = dayjs(selectedDate);

    // 如果已选择开始日期，限制结束日期不能超过6个月
    const maxEndDate = selectedStart.add(6, 'month');
    const minEndDate = selectedStart.subtract(6, 'month');

    return currentDate.isAfter(maxEndDate) || currentDate.isBefore(minEndDate);
  };

  // 日期范围变化处理
  const handleDateRangeChange = (_value: any, _date: any, dateStrings?: (string | undefined)[]) => {
    if (!dateStrings || dateStrings.length !== 2) {
      return;
    }

    const [startDate, endDate] = dateStrings;

    if (startDate && endDate && !validateDateRange(startDate, endDate)) {
      Message.warning('日期范围不能超过6个月，请重新选择');
      // 清空日期选择
      searchForm.date_range = [];
      // 清空时间范围选择
      searchForm.time_range = '';
      return;
    }

    // 如果验证通过，清空时间范围选择（因为用户手动选择了日期）
    searchForm.time_range = '';
  };

  // 初始化
  onMounted(() => {
    fetchData();
  });
</script>

<style scoped lang="less"></style>
