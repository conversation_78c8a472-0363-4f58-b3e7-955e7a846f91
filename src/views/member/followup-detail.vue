<template>
  <div class="base-box">
    <Breadcrumb />
    <!-- Search Form -->
    <a-card class="general-card">
      <a-form
        :model="searchForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width>
        <a-row :gutter="16">
          <a-col :flex="1">
            <a-form-item field="date_range" label="时间">
              <DateRangePicker
                v-model:model-value="searchForm.date_range"
                v-model:time-range="searchForm.time_range"
                @change="handleDateRangePickerChange" />
            </a-form-item>
          </a-col>
          <a-col :flex="1">
            <a-form-item field="username" label="会员昵称">
              <a-input v-model="searchForm.username" placeholder="请输入" allow-clear @press-enter="handleSearch" />
            </a-form-item>
          </a-col>
          <a-col :flex="1">
            <a-form-item field="coach_id" label="教练">
              <SalesSelect
                v-model="searchForm.coach_id"
                :belong-bus-id="searchForm.bus_id"
                label-in-value
                style="width: 300px"
                :is-membership="false"
                is-pt-coach
                is-swim-coach
                show-coach-type
                placeholder="请选择"></SalesSelect>
            </a-form-item>
          </a-col>
          <a-divider style="height: 32px" direction="vertical" />
          <a-col :flex="'86px'" style="text-align: right">
            <a-form-item>
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <icon-search />
                </template>
                搜索
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider style="margin-top: 0" />
        <a-row style="margin-bottom: 16px">
          <a-col :span="24" style="display: flex; align-items: center; justify-content: flex-end">
            <a-button @click="handleExport">导出Excel</a-button>
          </a-col>
        </a-row>
      </a-form>

      <!-- Table Section -->
      <a-table
        :loading="loading"
        :data="tableData"
        :pagination="pagination"
        :bordered="false"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange">
        <template #columns>
          <a-table-column title="序号" data-index="index" :width="80" align="center">
            <template #cell="{ rowIndex }">
              {{ (pagination.page_no - 1) * pagination.page_size + rowIndex + 1 }}
            </template>
          </a-table-column>
          <a-table-column title="会员姓名" data-index="username" align="center" />
          <a-table-column title="跟进内容" data-index="follow_content" align="center" />
          <a-table-column title="跟进时间" data-index="follow_time" align="center" />
          <a-table-column title="跟进人" data-index="follow_person" align="center" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import { useBusInfoStore } from '@/store';
  import SalesSelect from '@/components/membership/salesSelect.vue';
  import DateRangePicker from '@/components/form/date-range-picker.vue';
  import {
    getFollowupDetailList,
    exportFollowupDetailData,
    type FollowupDetailRecord,
    type FollowupDetailSearchParams,
  } from '@/api/followup';

  // 定义 props
  const props = defineProps<{
    coachId: string;
  }>();

  // 定义接口类型
  interface SearchForm {
    date_range: string[];
    time_range: string;
    coach_id: string;
    bus_id: string;
    username: string;
  }

  // 响应式数据
  const loading = ref(false);
  const tableData = ref<FollowupDetailRecord[]>([]);
  const coachName = ref('');

  const busInfo = useBusInfoStore();

  // 搜索表单
  const searchForm = reactive<SearchForm>({
    date_range: [],
    time_range: '',
    coach_id: props.coachId,
    bus_id: busInfo.bus_id,
    username: '',
  });

  // 分页配置
  const pagination = reactive({
    page_no: 1,
    page_size: 10,
    total: 0,
    showPageSize: true,
    showTotal: true,
  });

  // API 实例
  const { execute: executeGetList } = getFollowupDetailList();
  const { execute: executeExport } = exportFollowupDetailData();

  // 使用模拟数据作为后备
  const mockIt = () => {
    const mockData = {
      list: [
        {
          id: 1,
          username: '王明',
          follow_content: '电话沟通并解答',
          follow_time: '2025-05-12 16:57',
          follow_person: '张教练',
        },
        {
          id: 2,
          username: '王明',
          follow_content: '咨询意向并深度',
          follow_time: '2025-05-12 16:50',
          follow_person: '张教练',
        },
        {
          id: 3,
          username: '张**',
          follow_content: '数据量级不够',
          follow_time: '2025-05-12 14:50',
          follow_person: '张教练',
        },
      ],
      total: 3,
    };

    tableData.value = mockData.list;
    pagination.total = mockData.total;
    coachName.value = '张教练';
  };

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const params: FollowupDetailSearchParams = {
        coach_id: props.coachId,
        start_time: searchForm.date_range[0] || '',
        end_time: searchForm.date_range[1] || '',
        page_no: pagination.page_no,
        page_size: pagination.page_size,
        username: searchForm.username,
      };

      const { data } = await executeGetList({ data: params });

      if (data.value) {
        tableData.value = data.value.list;
        pagination.total = data.value.total;
      }

      mockIt();
    } catch (error) {
      console.error('获取数据失败:', error);
      mockIt();
    } finally {
      loading.value = false;
    }
  };

  // 方法定义
  const handleDateRangePickerChange = (dateRange: string[], timeRange: string) => {
    // 日期范围变化时的处理逻辑
    // 这里可以添加额外的业务逻辑，如自动搜索等
    console.log('Date range changed:', dateRange, timeRange);
  };

  const handleSearch = async () => {
    pagination.page_no = 1;
    await fetchData();
  };

  const handleExport = async () => {
    try {
      const params: FollowupDetailSearchParams = {
        coach_id: props.coachId,
        start_time: searchForm.date_range[0] || '',
        end_time: searchForm.date_range[1] || '',
        _export: 1,
      };

      await executeExport({ data: params });
      Message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      Message.error('导出失败');
    }
  };

  const handlePageChange = (page: number) => {
    pagination.page_no = page;
    fetchData();
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.page_size = pageSize;
    pagination.page_no = 1;
    fetchData();
  };

  // 初始化
  onMounted(() => {
    fetchData();
  });
</script>

<style scoped lang="less"></style>
