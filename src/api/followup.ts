import request from '@/request/index';

// 跟进教练数据接口
export interface FollowupCoachData {
  coach_id: number;
  coach_name: string;
  follow_user_num: number;
  follow_num: number;
}

// 搜索参数接口
export interface FollowupCoachSearchParams {
  start_time?: string;
  end_time?: string;
  coach_id?: string;
  page_no?: number;
  page_size?: number;
  _export?: number;
}

// 响应数据接口
export interface FollowupCoachListResponse {
  list: FollowupCoachData[];
  total: number;
}

// 获取跟进教练列表
export function getFollowupCoachList() {
  return request<FollowupCoachListResponse>({
    url: '/Web/Statistics/getCoachFollowStatistics',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 导出跟进教练数据
export function exportFollowupCoachData() {
  return request({
    url: '/Web/Statistics/getCoachFollowStatisticsExport',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 跟进详情记录数据接口
export interface FollowupDetailRecord {
  id: number;
  username: string;
  follow_content: string;
  follow_time: string;
  follow_person: string;
}

// 跟进详情搜索参数接口
export interface FollowupDetailSearchParams {
  coach_id: string;
  start_time?: string;
  end_time?: string;
  page_no?: number;
  page_size?: number;
  _export?: number;
  username?: string;
}

// 跟进详情响应数据接口
export interface FollowupDetailListResponse {
  list: FollowupDetailRecord[];
  total: number;
}

// 获取跟进详情记录列表
export function getFollowupDetailList() {
  return request<FollowupDetailListResponse>({
    url: '/Web/Statistics/getCoachFollowList',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 导出跟进详情数据
export function exportFollowupDetailData() {
  return request({
    url: '/Web/Statistics/getCoachFollowExport',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}
